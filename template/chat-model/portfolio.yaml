ui:
  - input: demand
    output:
      - vui lòng nhập

  - input: required
    output:
      - b<PERSON><PERSON> b<PERSON><PERSON><PERSON> nhập

  - input: portfolio_quantity
    output:
      - số lượng

  - input: tax
    output:
      - phần trăm thuế (0-100)

  - input: year
    output:
      - năm

  - input: portfolio_asset
    output:
      - mã cổ phiếu

  - input: unit_price
    output:
      - đơn giá

intentions:
  - intent: portfolio_long
    description: mua cổ phiếu
    context:
      url: /v1/portfolio/long-asset
      method: POST
      param_fixes:
        - category: stock
      params:
        - name: portfolio_asset
        - chat_id: chat_id
        - quantity: portfolio_quantity
        - cost_per_share: unit_price
        - tax: tax

  - intent: portfolio_short
    description: bán cổ phiếu
    context:
      url: /v1/portfolio/short-asset
      method: POST
      param_fixes:
        - category: stock
      params:
        - name: portfolio_asset
        - chat_id: chat_id
        - quantity: portfolio_quantity
        - cost_per_share: unit_price
        - tax: tax

  - intent: portfolio_fetch_price
    description: l<PERSON>y gi<PERSON> cổ phiếu
    context:
      url: /v1/portfolio/market-price
      method: POST
      params:
        - chat_id: chat_id

  - intent: fetch_price
    description: l<PERSON>y gi<PERSON> mới nhất của cổ phiếu
    context:
      url: /v1/stock/fetch-price
      method: POST
      params:
        - stock: stock

  - intent: fetch_price_cache
    description: lấy tất cả giá mới nhất của cổ phiếu
    context:
      url: /v1/stock/fetch-price/cache
      method: POST
      params:
        - stock: stock

  - intent: portfolio_answer
    description: trả lời giá trị đầu tư
    context:
      url: /v1/chatbot/send/template
      method: POST
      param_fixes:
        - template: asset-value
      params:
        - chat_id: chat_id
        - list: vars

  - intent: portfolio_gain_loss
    description: ghi nhận lời lỗ cổ phiếu đã đầu tư
    context:
      url: /v1/portfolio/pl
      method: POST
      params:
        - chat_id: chat_id

  - intent: portfolio_gain_loss_activity
    description: ghi nhận lời lỗ 1 cổ phiếu đã đầu tư
    context:
      url: /v1/portfolio/pl/activity
      method: POST
      params:
        - chat_id: chat_id
        - portfolio_id: portfolio_id
        - accumulation: accumulation
        - pnl: pnl

  - intent: portfolio_gain_loss_upsert
    description: lưu gain/loss activities vào earning
    context:
      url: /v1/portfolio/pl/upsert
      method: POST
      params:
        - chat_id: chat_id
        - accumulation: accumulation
        - pnl: pnl

  - intent: portfolio_gain_loss_insert
    description: thêm gain/loss activities vào earning
    context:
      url: /v1/portfolio/pl/insert
      method: POST
      params:
        - chat_id: chat_id
        - accumulation: accumulation
        - pnl: pnl

  - intent: portfolio_recently_upsert
    description: lưu portfolio tới thời điểm hiện tại
    context:
      url: /v1/portfolio/recently/upsert
      method: POST
      params:
        - chat_id: chat_id
        - accumulation: accumulation
        - pnl: pnl

  - intent: portfolio_value
    description: tính giá trị cổ phiếu đã đầu tư
    context:
      url: /v1/portfolio/value
      method: GET
      params:
        - chat_id: chat_id

  - intent: portfolio_calculate_weight
    description: tính trọng số cổ phiếu đã đầu tư
    context:
      url: /v1/portfolio/weight
      method: GET
      params:
        - chat_id: chat_id

  - intent: portfolio_calculate_sale
    description: tính điểm mua/bán cổ phiếu đã đầu tư
    context:
      url: /v1/portfolio/sale
      method: POST
      params:
        - chat_id: chat_id
        - assets: assets

  - intent: portfolio_calculate_variance
    description: tính rủi ro cổ phiếu đã đầu tư
    context:
      url: /v1/portfolio/var
      method: POST
      params:
        - assets: assets

  - intent: portfolio_calculate_new_variance
    description: tính rủi ro sau khi tối ưu tỷ trọng cổ phiếu đã đầu tư
    context:
      url: /v1/portfolio/var
      method: POST
      params:
        - assets: new_weights

  - intent: portfolio_risk_answer
    description: trả lời rủi ro cổ phiếu đã đầu tư
    context:
      url: /v1/chatbot/send/template
      method: POST
      param_fixes:
        - template: stock-risk
      params:
        - chat_id: chat_id

  - intent: portfolio_calculate_velocity
    description: so sánh tốc độ tăng giá cổ phiếu đã đầu tư
    context:
      url: /v1/portfolio/velocity
      method: POST
      params:
        - assets: assets

  - intent: portfolio_calculate_balance
    description: cân bằng cổ phiếu đã đầu tư
    context:
      url: /v1/portfolio/balance
      method: POST
      params:
        - assets: assets
        - new_weights: new_weights
        - var: var

  - intent: portfolio_auto_allocate_answer
    description: trả lời tự động đầu tư
    context:
      url: /v1/chatbot/send/template
      method: POST
      param_fixes:
        - template: stock-allocate
      params:
        - chat_id: chat_id
        - list: suggestion

  - intent: portfolio_dividend_recently
    description: tải cổ tức cổ phiếu đang đầu tư
    context:
      url: /v1/dividend/download/recently
      method: POST
      params:
        - chat_id: chat_id

  - intent: portfolio_dividend_aggregate
    description: tổng hợp cổ tức cổ phiếu đã đầu tư
    context:
      url: /v1/dividend/aggregation
      method: POST
      params:
        - chat_id: chat_id
        - year: portfolio_year

  - intent: portfolio_dividend_aggregate_answer
    description: trả lời cổ tức cổ phiếu đã đầu tư
    context:
      url: /v1/chatbot/send/template
      method: POST
      param_fixes:
        - template: dividend-aggregation
      params:
        - chat_id: chat_id
        - list: divs

  - intent: portfolio_dividend_aggregate_total_answer
    description: trả lời rủi ro cổ phiếu đã đầu tư
    context:
      url: /v1/chatbot/send/template
      method: POST
      param_fixes:
        - template: dividend-total
      params:
        - chat_id: chat_id
        - list: all

workflows:
  - topic: portfolio_long
    description: thêm cổ phiếu vào portfolio
    error_stop: true
    keywords:
      - mua (1000)[portfolio_quantity] cổ phiếu (vnm)[portfolio_asset]
      - mua cổ phiếu (vnm)[portfolio_asset]
      - đã mua (1000)[portfolio_quantity] cổ phiếu (vnm)[portfolio_asset]
      - đã mua cổ phiếu (vnm)[portfolio_asset]
      - thêm (100)[portfolio_quantity] cổ phiếu (ctg)[portfolio_asset]
      - thêm cổ phiếu (ctg)[portfolio_asset]
      - nhập (10)[portfolio_quantity] cổ phiếu (vnm)[portfolio_asset]
      - nhập cổ phiếu (vnm)[portfolio_asset]
    intents:
      - portfolio_long
      - portfolio_gain_loss_insert
      - portfolio_recently_upsert
      - portfolio_fetch_price
      - portfolio_value
      - portfolio_answer

  - topic: portfolio_short
    description: bán cổ phiếu trong portfolio
    error_stop: true
    keywords:
      - bán (1000)[portfolio_quantity] cổ phiếu (vnm)[portfolio_asset]
      - bán cổ phiếu (vnm)[portfolio_asset]
      - đã bán (1000)[portfolio_quantity] cổ phiếu (vnm)[portfolio_asset]
      - đã bán cổ phiếu (vnm)[portfolio_asset]
      - xóa (10)[portfolio_quantity] cổ phiếu (vnm)[portfolio_asset]
      - xóa cổ phiếu (vnm)[portfolio_asset]
      - đã xóa (10)[portfolio_quantity] cổ phiếu (vnm)[portfolio_asset]
      - đã xóa cổ phiếu (vnm)[portfolio_asset]
      - bỏ (100)[portfolio_quantity] cổ phiếu (ctg)[portfolio_asset]
      - bỏ cổ phiếu (ctg)[portfolio_asset]
      - đã bỏ (100)[portfolio_quantity] cổ phiếu (ctg)[portfolio_asset]
      - đã bỏ cổ phiếu (ctg)[portfolio_asset]
    intents:
      - portfolio_short
      - portfolio_gain_loss_insert
      - portfolio_recently_upsert
      - portfolio_fetch_price
      - portfolio_value
      - portfolio_answer

  - topic: portfolio_value
    description: xem giá trị portfolio
    error_stop: false
    keywords:
      - xem giá trị cổ phiếu
      - xem giá trị portfolio
      - xem portfolio
      - xem chi tiết đầu tư
      - xem giá trị đầu tư
    intents:
      - portfolio_fetch_price
      - portfolio_value
      - portfolio_answer

  - topic: portfolio_allocate
    description: điều chỉnh giá trị portfolio
    error_stop: false
    keywords:
      - cân chỉnh portfolio
      - điều chỉnh giá trị đầu tư
      - chỉnh portfolio
    intents:
      - portfolio_fetch_price
      - portfolio_calculate_weight
      - portfolio_calculate_sale
      - portfolio_auto_allocate_answer

  - topic: portfolio_dividend_download
    description: tải cổ tức của portfolio
    error_stop: false
    keywords:
      - download portfolio dividend
      - tải cổ tức đầu tư
    intents:
      - portfolio_dividend_recently

  - topic: portfolio_profit_and_loss_calculating
    description: calculate gain and loss of portfolio
    error_stop: false
    keywords:
      - Tính toán thu nhập portfolio
      - Tính toán giá trị portfolio
      - Tính giá trị danh mục
    intents:
      - portfolio_gain_loss
      - portfolio_gain_loss_upsert
      - portfolio_recently_upsert
      - portfolio_fetch_price
      - portfolio_value
      - portfolio_answer

  - topic: portfolio_dividend_aggregation
    description: tổng hợp cổ tức của portfolio trong năm
    error_stop: true
    keywords:
      - cổ tức trong năm (2022)[portfolio_year]
      - cổ tức đã đầu tư năm (2022)[portfolio_year]
      - cổ tức năm (2022)[portfolio_year]
      - tổng cổ tức trong năm (2022)[portfolio_year]
      - tổng cổ tức năm (2022)[portfolio_year]
    intents:
      - portfolio_dividend_aggregate
      - portfolio_dividend_aggregate_answer
      - portfolio_dividend_aggregate_total_answer
