package portfolio

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/google/uuid"

	"github.com/007lock/simon-homestead/internal/constants"
	"github.com/007lock/simon-homestead/pkg/model"
	"github.com/007lock/simon-homestead/pkg/service"
	"github.com/labstack/echo/v4"
	"github.com/lib/pq"
)

func LongAsset(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type assReqModel struct {
			ChatID       string `json:"chat_id" validate:"required"`
			Name         string `json:"name" validate:"required"`
			Quantity     string `json:"quantity" validate:"required,numeric"`
			CostPerShare string `json:"cost_per_share" validate:"numeric"`
			Tax          string `json:"tax" validate:"numeric"`
			Category     string `json:"category" validate:"required"`
		}

		assReq := new(assReqModel)

		err := c.Bind(assReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(assReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)

		systemCode := assReq.Name
		if assReq.Category == constants.AssetCategories.STOCK {
			stmt, err := tx.Prepare("SELECT id FROM stocks WHERE id LIKE UPPER($1)")
			if err != nil {
				return err
			}
			r := stmt.QueryRowContext(c.Request().Context(), assReq.Name)
			if err := r.Scan(&systemCode); err != nil {
				if errors.Is(err, sql.ErrNoRows) {
					return echo.NewHTTPError(http.StatusInternalServerError, constants.CommonError.ERROR_NOT_FOUND)
				}
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		} else if assReq.Category == constants.AssetCategories.GOLD {
			stmt, err := tx.Prepare("SELECT id FROM commodities WHERE id LIKE UPPER($1)")
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			r := stmt.QueryRowContext(c.Request().Context(), assReq.Name)
			if err := r.Scan(&systemCode); err != nil {
				if errors.Is(err, sql.ErrNoRows) {
					return echo.NewHTTPError(http.StatusInternalServerError, constants.CommonError.ERROR_NOT_FOUND)
				}
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}
		// Insert
		stmt, err := tx.PrepareContext(c.Request().Context(), "INSERT INTO portfolios(id, stock_id, user_id, category, quantity, price, tax, buy_at) VALUES($1,$2,$3,$4,$5,$6,$7,$8)")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		loc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
		// Detect user input hours
		uuidKey, err := uuid.NewV7()
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		buyTime := time.Now().In(loc)
		_, err = stmt.ExecContext(c.Request().Context(), uuidKey.String(), systemCode, assReq.ChatID, assReq.Category, assReq.Quantity, assReq.CostPerShare, assReq.Tax, buyTime)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		// Query accumulated in current_portfolios
		accumulated := make(map[string]*model.PorfolioAccumulateStock)
		stmt, err = tx.PrepareContext(c.Request().Context(), "SELECT stock, quantity, book_value, buy_at FROM current_portfolios WHERE user_id = $1")

		rs, err := stmt.QueryContext(c.Request().Context(), assReq.ChatID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		for rs.Next() {
			accStock := new(model.PorfolioAccumulateStock)
			var stockCode string
			err = rs.Scan(&stockCode, &accStock.Quantity, &accStock.BookValue, &accStock.LatestBoughtAt)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			accumulated[stockCode] = accStock
		}
		err = rs.Close()
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		// Calculate sell activity
		intentParams, err := json.Marshal(map[string]interface{}{
			"portfolio_id": uuidKey.String(),
		})
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		jobMap := map[int]string{
			0: fmt.Sprintf("portfolio_gain_loss_activity(%s)", intentParams),
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			constants.JobDefination.EXPANDING: jobMap,
			"accumulation":                    accumulated,
			"pnl":                             make([]*model.PortfolioProfitNLoss, 0),
		})
	}
}

func ShortAsset(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type assReqModel struct {
			ChatID       string `json:"chat_id" validate:"required"`
			Name         string `json:"name" validate:"required"`
			Quantity     string `json:"quantity" validate:"required,numeric"`
			CostPerShare string `json:"cost_per_share" validate:"numeric"`
			Tax          string `json:"tax" validate:"numeric"`
			Category     string `json:"category" validate:"required"`
		}

		assReq := new(assReqModel)

		err := c.Bind(assReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(assReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		quantity, err := strconv.ParseFloat(assReq.Quantity, 64)
		if err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)

		systemCode := assReq.Name
		if assReq.Category == constants.AssetCategories.STOCK {
			stmt, err := tx.Prepare("SELECT id FROM stocks WHERE id LIKE UPPER($1)")
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			r := stmt.QueryRowContext(c.Request().Context(), assReq.Name)
			if err := r.Scan(&systemCode); err != nil {
				if errors.Is(err, sql.ErrNoRows) {
					return echo.NewHTTPError(http.StatusInternalServerError, constants.CommonError.ERROR_NOT_FOUND)
				}
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		} else if assReq.Category == constants.AssetCategories.GOLD {
			stmt, err := tx.Prepare("SELECT id FROM commodities WHERE id LIKE UPPER($1)")
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			r := stmt.QueryRowContext(c.Request().Context(), assReq.Name)
			if err := r.Scan(&systemCode); err != nil {
				if errors.Is(err, sql.ErrNoRows) {
					return echo.NewHTTPError(http.StatusInternalServerError, constants.CommonError.ERROR_NOT_FOUND)
				}
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
		}
		// Insert
		stmt, err := tx.PrepareContext(c.Request().Context(), "INSERT INTO portfolios(id, stock_id, user_id, category, quantity, price, tax, buy_at) VALUES($1,$2,$3,$4,$5,$6,$7,$8)")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		loc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
		// Detect user input hours
		uuidKey, err := uuid.NewV7()
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		buyTime := time.Now().In(loc)
		_, err = stmt.ExecContext(c.Request().Context(), uuidKey.String(), systemCode, assReq.ChatID, assReq.Category, -quantity, assReq.CostPerShare, assReq.Tax, buyTime)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		// Query accumulated in current_portfolios
		accumulated := make(map[string]*model.PorfolioAccumulateStock)
		stmt, err = tx.PrepareContext(c.Request().Context(), "SELECT stock, quantity, book_value, buy_at FROM current_portfolios WHERE user_id = $1")

		rs, err := stmt.QueryContext(c.Request().Context(), assReq.ChatID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		for rs.Next() {
			accStock := new(model.PorfolioAccumulateStock)
			var stockCode string
			err = rs.Scan(&stockCode, &accStock.Quantity, &accStock.BookValue, &accStock.LatestBoughtAt)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			accumulated[stockCode] = accStock
		}
		err = rs.Close()
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		// Calculate sell activity
		intentParams, err := json.Marshal(map[string]interface{}{
			"portfolio_id": uuidKey.String(),
		})
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		jobMap := map[int]string{
			0: fmt.Sprintf("portfolio_gain_loss_activity(%s)", intentParams),
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			constants.JobDefination.EXPANDING: jobMap,
			"accumulation":                    accumulated,
			"pnl":                             make([]*model.PortfolioProfitNLoss, 0),
		})
	}
}

func FetchPricePortfolio(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type reqModel struct {
			ChatID string `json:"chat_id" validate:"required"`
		}
		req := new(reqModel)

		err := c.Bind(req)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(req); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.Tx)
		// Query Recently Stocks
		stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT stock FROM current_portfolios WHERE user_id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs, err := stmt.QueryContext(c.Request().Context(), req.ChatID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		var stocks []string
		defer rs.Close()
		for rs.Next() {
			var assetID string

			err := rs.Scan(&assetID)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			stocks = append(stocks, assetID)
		}
		// Build map
		totalStocks := len(stocks)
		jobMap := make(map[int]string, totalStocks)
		for i, stock := range stocks {
			intentParams, err := json.Marshal(map[string]interface{}{
				"stock": stock,
			})
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			jobMap[i] = fmt.Sprintf("fetch_price(%s)", intentParams)
		}
		for i, stock := range stocks {
			intentParams, err := json.Marshal(map[string]interface{}{
				"stock": stock,
			})
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			jobMap[i+totalStocks] = fmt.Sprintf("fetch_price_cache(%s)", intentParams)
		}
		// Set flag to nudge queue after transaction commit
		c.Set(constants.NudgeQueueKey, true)

		return c.JSON(http.StatusOK, map[string]interface{}{
			constants.JobDefination.EXPANDING: jobMap,
			"stocks":                          stocks,
		})
	}
}

func CalculateStockAsset(s *service.Service) echo.HandlerFunc {
	return func(c echo.Context) error {
		type assReqModel struct {
			ChatID string `json:"chat_id" query:"chat_id" validate:"required"`
		}

		assReq := new(assReqModel)

		err := c.Bind(assReq)
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, err)
		}

		if err := c.Validate(assReq); err != nil {
			return echo.NewHTTPError(http.StatusUnprocessableEntity, err)
		}

		tx := c.Request().Context().Value(constants.ContextTransactionKey).(*sql.DB)
		type AssetBookValue struct {
			quantity  float64
			bookValue float64
		}
		stockMap := make(map[string]AssetBookValue)
		var stocks []string
		// Query Recently Stocks
		stmt, err := tx.PrepareContext(c.Request().Context(), "SELECT stock, quantity, book_value FROM current_portfolios WHERE user_id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs1, err := stmt.QueryContext(c.Request().Context(), assReq.ChatID)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		defer rs1.Close()
		for rs1.Next() {
			var (
				assetID   string
				quantity  float64
				bookValue float64
			)
			err := rs1.Scan(&assetID, &quantity, &bookValue)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			stocks = append(stocks, assetID)
			stockMap[assetID] = AssetBookValue{
				quantity:  quantity,
				bookValue: bookValue,
			}
		}

		// Query prices
		stmt, err = tx.Prepare("SELECT id, price FROM stocks WHERE id LIKE ANY($1)")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		rs2, err := stmt.QueryContext(c.Request().Context(), pq.StringArray(stocks))
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		bookValue := 0.0
		fairValue := 0.0
		defer rs2.Close()
		for rs2.Next() {
			var (
				code  string
				price float64
			)
			err := rs2.Scan(&code, &price)
			if err != nil {
				return echo.NewHTTPError(http.StatusInternalServerError, err)
			}
			quantity := stockMap[code].quantity
			if quantity != 0 {
				bookValue += stockMap[code].bookValue
				fairValue += price * quantity
			}
		}

		// Query cash asset
		stmt, err = tx.PrepareContext(c.Request().Context(), "SELECT COALESCE(SUM(profits), 0) FROM pl_portfolios WHERE user_id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		r := stmt.QueryRowContext(c.Request().Context(), assReq.ChatID)
		var totalCash float64
		err = r.Scan(&totalCash)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		gain := (fairValue+totalCash)/bookValue - 1
		// Create token
		stmt, err = tx.Prepare("SELECT username, first_name, last_name FROM chatbot WHERE id = $1")
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		userRow := stmt.QueryRowContext(c.Request().Context(), assReq.ChatID)
		user := &model.UserInfo{ID: assReq.ChatID}
		if err := userRow.Scan(&user.User, &user.FirstName, &user.LastName); err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}
		token, err := s.JWTs.Sign(user)
		if err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, err)
		}

		return c.JSON(
			http.StatusOK,
			map[string]interface{}{
				"vars": []map[string]interface{}{
					{
						"cash":         s.Telegram.NumberFormat(int64(totalCash)),
						"fair_value":   s.Telegram.NumberFormat(int64(fairValue)),
						"book_value":   s.Telegram.NumberFormat(int64(bookValue)),
						"market_value": s.Telegram.NumberFormat(int64(fairValue)),
						"gainorloss":   s.Telegram.NumberFormat(gain * 100),
						"token":        token,
						"frontend_url": s.Config.FrontendURL,
					},
				},
			})
	}
}
